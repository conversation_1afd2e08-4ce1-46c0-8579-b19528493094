<?php
require_once 'includes/db.php';
require_once 'includes/auth.php';

// Test the calendar_summary API
$db = get_db();

// Test for room 5 (Cosmetology) on 2025-06-01
$room_id = 5;
$date = '2025-06-01';

echo "<h2>Testing Calendar Summary API</h2>";
echo "<p>Room ID: $room_id, Date: $date</p>";

// Get consultation count
$stmt = $db->prepare("
    SELECT COUNT(*) as count FROM appointments 
    WHERE room_id = ? AND appointment_date = ? AND type = 'consult'
");
$stmt->execute([$room_id, $date]);
$consult_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Get cosmetic count
$stmt = $db->prepare("
    SELECT COUNT(*) as count FROM appointments 
    WHERE room_id = ? AND appointment_date = ? AND type = 'cosmetic'
");
$stmt->execute([$room_id, $date]);
$cosmetic_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Check for surgery
$stmt = $db->prepare("
    SELECT s.id, p.name as patient_name 
    FROM room_reservations rr
    JOIN surgeries s ON rr.surgery_id = s.id
    JOIN patients p ON s.patient_id = p.id
    WHERE rr.room_id = ? AND rr.reserved_date = ?
");
$stmt->execute([$room_id, $date]);
$surgery = $stmt->fetch(PDO::FETCH_ASSOC);

echo "<h3>Results:</h3>";
echo "<p>Consultation count: $consult_count</p>";
echo "<p>Cosmetic count: $cosmetic_count</p>";
echo "<p>Surgery: " . ($surgery ? 'Yes - ' . $surgery['patient_name'] : 'No') . "</p>";

// Test all appointments for this date
echo "<h3>All appointments for $date:</h3>";
$stmt = $db->prepare("
    SELECT a.*, p.name as patient_name, r.name as room_name
    FROM appointments a
    JOIN patients p ON a.patient_id = p.id
    JOIN rooms r ON a.room_id = r.id
    WHERE a.appointment_date = ?
    ORDER BY a.room_id, a.start_time
");
$stmt->execute([$date]);
$appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1'>";
echo "<tr><th>Room</th><th>Patient</th><th>Type</th><th>Subtype</th><th>Time</th></tr>";
foreach ($appointments as $apt) {
    echo "<tr>";
    echo "<td>{$apt['room_name']}</td>";
    echo "<td>{$apt['patient_name']}</td>";
    echo "<td>{$apt['type']}</td>";
    echo "<td>{$apt['subtype']}</td>";
    echo "<td>{$apt['start_time']} - {$apt['end_time']}</td>";
    echo "</tr>";
}
echo "</table>";

// Test the API endpoint directly
echo "<h3>Testing API endpoint:</h3>";
$_GET['room_id'] = $room_id;
$_GET['date'] = $date;
include_once 'api_handlers/calendar_summary.php';
$result = handle_calendar_summary('', 'GET', $db, []);
echo "<pre>" . print_r($result, true) . "</pre>";
?>
