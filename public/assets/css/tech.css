.availability-cell {
    min-height: 80px;
    padding: 4px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    border: 1px solid #dee2e6;
    /* Added border */
}

.availability-cell.unavailable {
    background-color: #ffffff;
    border-color: #dee2e6;
}

.availability-cell.unavailable:hover {
    background-color: #f8f9fa;
}

.availability-cell.available-am {
    background: linear-gradient(to bottom, #d4edda 0%, #d4edda 50%, #ffffff 50%, #ffffff 100%);
    border-color: #c3e6cb;
}

.availability-cell.available-pm {
    background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #d4edda 50%, #d4edda 100%);
    border-color: #c3e6cb;
}

.availability-cell.available-full {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.availability-cell.inactive {
    background-color: #e2e3e5;
    border-color: #d6d8db;
    cursor: not-allowed;
}

.availability-cell:hover:not(.inactive) {
    opacity: 0.8;
    transform: scale(1.02);
}

.date-col {
    min-width: 200px;
    background-color: #f8f9fa;
    font-weight: bold;
}

#availability-table th {
    text-align: center;
    vertical-align: middle;
}

.tech-name {
    font-weight: bold;
    background-color: #f8f9fa;
    min-width: 200px;
    cursor: pointer;
}

.tech-name:hover {
    background-color: #e9ecef;
}

.tech-specialty {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}
