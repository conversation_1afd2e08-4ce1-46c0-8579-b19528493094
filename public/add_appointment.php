<?php
require_once 'includes/db.php';
require_once 'includes/auth.php';

// Ensure user is logged in
if (!is_logged_in()) {
    header('Location: login.php');
    exit();
}

// Get pre-filled values from URL parameters
$room_id = $_GET['room_id'] ?? '';
$date = $_GET['date'] ?? '';

$page_title = "Add Appointment";
require_once 'includes/header.php';
?>

<div class="container-fluid mt-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-calendar-plus me-2 text-success"></i>
            Add New Appointment
        </h2>
        <div class="btn-group" role="group">
            <a href="appointments.php" class="btn btn-secondary">
                <i class="fas fa-list me-1"></i>
                View All Appointments
            </a>
            <a href="calendar.php" class="btn btn-primary">
                <i class="fas fa-calendar me-1"></i>
                Calendar View
            </a>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="status-messages"></div>
    <!-- Quick Time Slots Helper -->
    <div class="row justify-content-center mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Quick Time Slots
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="setTimeSlot('09:00', '10:00')">
                                09:00 - 10:00
                            </button>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="setTimeSlot('10:00', '11:00')">
                                10:00 - 11:00
                            </button>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="setTimeSlot('11:00', '12:00')">
                                11:00 - 12:00
                            </button>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="setTimeSlot('14:00', '15:00')">
                                14:00 - 15:00
                            </button>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="setTimeSlot('15:00', '16:00')">
                                15:00 - 16:00
                            </button>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="setTimeSlot('16:00', '17:00')">
                                16:00 - 17:00
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Add Appointment Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        Appointment Details
                    </h5>
                </div>
                <div class="card-body">
                    <form id="appointment-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="patient-id" class="form-label">Patient *</label>
                                    <div class="input-group">
                                        <select class="form-select" id="patient-id">
                                            <option value="">Select Patient</option>
                                        </select>
                                        <button type="button" class="btn btn-success" data-bs-toggle="modal"
                                            data-bs-target="#newPatientModal">
                                            <i class="fas fa-plus me-1"></i>
                                            <span class="d-none d-sm-inline">New Patient</span>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        Please select a patient.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="room-id" class="form-label">Room *</label>
                                    <select class="form-select" id="room-id">
                                        <option value="">Select Room</option>
                                    </select>
                                    <div class="invalid-feedback">

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="appointment-date" class="form-label">Date *</label>
                                    <input type="date" class="form-control" id="appointment-date">
                                    <div class="invalid-feedback">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="start-time" class="form-label">Start Time *</label>
                                    <input type="time" class="form-control" id="start-time">
                                    <div class="invalid-feedback">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="end-time" class="form-label">End Time *</label>
                                    <input type="time" class="form-control" id="end-time">
                                    <div class="invalid-feedback">

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Type *</label>
                                    <select class="form-select" id="type">
                                        <option value="">Select Type</option>
                                        <option value="consult">Consultation</option>
                                        <option value="cosmetic">Cosmetic</option>
                                    </select>
                                    <div class="invalid-feedback">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subtype" class="form-label">Subtype</label>
                                    <input type="text" class="form-control" id="subtype"
                                        placeholder="e.g., Online, Botox, PRP">
                                    <div class="form-text">
                                        Optional: Specify consultation method or cosmetic procedure
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" rows="3"
                                placeholder="Additional notes or special instructions"></textarea>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="appointments.php" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>
                                Create Appointment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Patient Modal -->
<div class="modal fade" id="newPatientModal" tabindex="-1" aria-labelledby="newPatientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-fullscreen-sm-down">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newPatientModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    Create New Patient
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="new-patient-form">
                    <?php if (is_admin() || is_editor()): ?>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="new_patient_agency_id" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    Agency
                                </label>
                                <select class="form-select" id="new_patient_agency_id" name="agency_id">
                                    <option value="">Select Agency ()</option>
                                    <!-- Agency options will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <?php elseif (is_agent()): ?>
                    <!-- Hidden field for agents - their agency_id will be set via JavaScript -->
                    <input type="hidden" id="new_patient_agency_id" name="agency_id" value="">
                    <?php endif; ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_patient_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Patient Name *
                                </label>
                                <input type="text" class="form-control" id="new_patient_name" name="name"
                                    placeholder="Enter patient name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_patient_dob" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    Date of Birth
                                </label>
                                <input type="date" class="form-control" id="new_patient_dob" name="dob">
                            </div>
                        </div>
                    </div>
                </form>
                <div id="new-patient-status"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="save-new-patient">
                    <i class="fas fa-save me-1"></i>Create Patient
                </button>
            </div>
        </div>
    </div>
</div>

</div>

<script>
let rooms = [];
let patients = [];
let agencies = [];
let formWasSubmitted = false; // Flag to track if form has been submitted

document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();

    // Form submission
    document.getElementById('appointment-form').addEventListener('submit', function(e) {
        e.preventDefault();
        formWasSubmitted = true; // Set flag on first submit

        if (validateForm()) {
            createAppointment();
        } else {
            // Form validation failed - submission is prevented
            console.log('Form validation failed. Submission prevented.');
        }
    });

    // Pre-fill form if URL parameters are provided
    if ('<?php echo $room_id; ?>') {
        document.getElementById('room-id').value = '<?php echo $room_id; ?>';
    }
    if ('<?php echo $date; ?>') {
        document.getElementById('appointment-date').value = '<?php echo $date; ?>';
    }

    // Set default date to today if not provided
    if (!document.getElementById('appointment-date').value) {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('appointment-date').value = today;
    }

    // Time validation
    document.getElementById('start-time').addEventListener('change', validateTimes);
    document.getElementById('end-time').addEventListener('change', validateTimes);

    // Add real-time validation for required fields (only after first submission)
    const requiredFields = ['patient-id', 'room-id', 'appointment-date', 'start-time', 'end-time', 'type'];
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        field.addEventListener('change', function() {
            if (formWasSubmitted) {
                validateSingleField(fieldId);
            }
        });
    });
});

async function loadInitialData() {
    try {
        // Load rooms, patients, and agencies in parallel
        const [roomsResponse, patientsResponse, agenciesResponse] = await Promise.all([
            fetch('api.php?entity=rooms&action=list'),
            fetch('api.php?entity=patients&action=list'),
            fetch('api.php?entity=agencies&action=list')
        ]);

        const roomsData = await roomsResponse.json();
        const patientsData = await patientsResponse.json();
        const agenciesData = await agenciesResponse.json();

        if (roomsData.success) {
            rooms = roomsData.rooms || [];
            populateRoomSelect();
        } else {
            displayMessage('Error loading rooms: ' + (roomsData.error || 'Unknown error'), 'danger');
        }

        if (patientsData.success) {
            patients = patientsData.patients || [];
            populatePatientSelect();
        } else {
            displayMessage('Error loading patients: ' + (patientsData.error || 'Unknown error'), 'danger');
        }

        if (agenciesData.success) {
            console.log('Agencies Data:', agenciesData); // Debugging line
            agencies = agenciesData.agencies || [];
            populateModalAgencyDropdown();
        } else {
            console.error('Error loading agencies:', agenciesData.error);
        }
    } catch (error) {
        console.error('Error loading data:', error);
        displayMessage('Failed to load data. Please try again.', 'danger');
    }
}

function populateRoomSelect() {
    const select = document.getElementById('room-id');

    // Clear existing options (except first)
    select.innerHTML = '<option value="">Select Room</option>';

    rooms.forEach(room => {
        if (room.is_active) {
            const option = new Option(room.name, room.id);
            select.add(option);
        }
    });

    // Re-select pre-filled value if it exists
    if ('<?php echo $room_id; ?>') {
        select.value = '<?php echo $room_id; ?>';
    }
}

function populatePatientSelect() {
    const select = document.getElementById('patient-id');

    // Clear existing options (except first)
    select.innerHTML = '<option value="">Select Patient</option>';

    patients.forEach(patient => {
        const option = new Option(patient.name, patient.id);
        select.add(option);
    });
}

function populateModalAgencyDropdown() {
    const agencySelect = document.getElementById('new_patient_agency_id');
    const userRole = '<?php echo get_user_role(); ?>';
    const userAgencyId = '<?php echo get_user_agency_id(); ?>';

    if (!agencySelect) return; // Field might not exist for all roles

    if (userRole === 'agent') {
        // For agents, set their agency_id in the hidden field
        agencySelect.value = userAgencyId;
    } else if (userRole === 'admin' || userRole === 'editor') {
        // For admin/editor, populate the dropdown
        agencySelect.innerHTML = '<option value="">Select Agency </option>';
        agencies.forEach(agency => {
            console.log('Agency:', agency);
            const option = new Option(agency.name, agency.id);
            agencySelect.add(option);
        });
    }
}

function validateForm() {
    const form = document.getElementById('appointment-form');
    let isValid = true;

    // Clear previous validation states and messages
    form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

    // Validate required fields
    const requiredFields = [{
            id: 'patient-id',
            message: 'Please select a patient.'
        },
        {
            id: 'room-id',
            message: 'Please select a room.'
        },
        {
            id: 'appointment-date',
            message: 'Please select a date.'
        },
        {
            id: 'start-time',
            message: 'Please enter a start time.'
        },
        {
            id: 'end-time',
            message: 'Please enter an end time.'
        },
        {
            id: 'type',
            message: 'Please select a type.'
        }
    ];

    requiredFields.forEach(fieldInfo => {
        const field = document.getElementById(fieldInfo.id);
        const feedback = field.nextElementSibling; // Get the next sibling, which is the invalid-feedback div
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            feedback.textContent = fieldInfo.message;
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
            feedback.textContent = '';
        }
    });

    // Validate time logic
    const startTime = document.getElementById('start-time').value;
    const endTime = document.getElementById('end-time').value;
    const endTimeFeedback = document.getElementById('end-time').nextElementSibling;

    if (startTime && endTime && startTime >= endTime) {
        document.getElementById('end-time').classList.add('is-invalid');
        endTimeFeedback.textContent = 'End time must be after start time.';
        isValid = false;
    } else {
        document.getElementById('end-time').classList.remove('is-invalid');
        endTimeFeedback.textContent = '';
    }

    return isValid;
}

function validateSingleField(fieldId) {
    const field = document.getElementById(fieldId);
    const feedback = field.nextElementSibling; // Get the next sibling, which is the invalid-feedback div

    if (!field.value.trim()) {
        field.classList.add('is-invalid');
        // Set specific message based on field ID
        switch (fieldId) {
            case 'patient-id':
                feedback.textContent = 'Please select a patient.';
                break;
            case 'room-id':
                feedback.textContent = 'Please select a room.';
                break;
            case 'appointment-date':
                feedback.textContent = 'Please select a date.';
                break;
            case 'start-time':
                feedback.textContent = 'Please enter a start time.';
                break;
            case 'end-time':
                feedback.textContent = 'Please enter an end time.';
                break;
            case 'type':
                feedback.textContent = 'Please select a type.';
                break;
            default:
                feedback.textContent = 'This field is required.';
        }
    } else {
        field.classList.remove('is-invalid');
        feedback.textContent = ''; // Clear message when valid
    }
}

function validateTimes() {
    // Only validate times if form has been submitted before
    if (!formWasSubmitted) {
        return;
    }

    const startTime = document.getElementById('start-time').value;
    const endTime = document.getElementById('end-time').value;
    const endTimeFeedback = document.getElementById('end-time').nextElementSibling;

    if (startTime && endTime && startTime >= endTime) {
        document.getElementById('end-time').classList.add('is-invalid');
        endTimeFeedback.textContent = 'End time must be after start time.';
    } else {
        document.getElementById('end-time').classList.remove('is-invalid');
        endTimeFeedback.textContent = '';
    }
}

function setTimeSlot(startTime, endTime) {
    document.getElementById('start-time').value = startTime;
    document.getElementById('end-time').value = endTime;
    validateTimes();
}

function createAppointment() {
    const formData = new FormData();
    formData.append('entity', 'appointments');
    formData.append('action', 'create');
    formData.append('patient_id', document.getElementById('patient-id').value);
    formData.append('room_id', document.getElementById('room-id').value);
    formData.append('appointment_date', document.getElementById('appointment-date').value);
    formData.append('start_time', document.getElementById('start-time').value);
    formData.append('end_time', document.getElementById('end-time').value);
    formData.append('type', document.getElementById('type').value);
    formData.append('subtype', document.getElementById('subtype').value);
    formData.append('notes', document.getElementById('notes').value);

    // Disable submit button to prevent double submission
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

    fetch('api.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayMessage('Appointment created successfully!', 'success');
                // Redirect to appointments list after a short delay
                setTimeout(() => {
                    window.location.href = 'appointments.php';
                }, 1500);
            } else {
                displayMessage('Error: ' + (data.error || 'Unknown error'), 'danger');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        })
        .catch(error => {
            console.error('Error creating appointment:', error);
            displayMessage('Failed to create appointment. Please try again.', 'danger');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
}

function displayMessage(message, type) {
    const messagesContainer = document.getElementById('status-messages');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    messagesContainer.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// New Patient Modal functionality
const newPatientModal = document.getElementById('newPatientModal');
const saveNewPatientButton = document.getElementById('save-new-patient');
const newPatientForm = document.getElementById('new-patient-form');
const newPatientStatusDiv = document.getElementById('new-patient-status');

// Handle new patient modal submission
if (saveNewPatientButton) {
    saveNewPatientButton.addEventListener('click', function() {
        const formData = new FormData(newPatientForm);
        formData.append('entity', 'patients');
        formData.append('action', 'add');

        newPatientStatusDiv.innerHTML = ''; // Clear previous status

        // Disable button to prevent double submission
        const originalText = saveNewPatientButton.innerHTML;
        saveNewPatientButton.disabled = true;
        saveNewPatientButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

        fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    newPatientStatusDiv.innerHTML =
                        '<div class="alert alert-success">Patient created successfully!</div>';

                    // Add the new patient to the select dropdown
                    const patientSelect = document.getElementById('patient-id');
                    if (patientSelect) {
                        const newOption = new Option(data.patient.name, data.patient.id, true, true);
                        patientSelect.add(newOption);
                    }

                    // Close the modal after a short delay
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(newPatientModal);
                        modal.hide();
                    }, 1000);
                } else {
                    newPatientStatusDiv.innerHTML =
                        `<div class="alert alert-danger">${data.error || 'An error occurred.'}</div>`;
                }
            })
            .catch(error => {
                console.error('Error creating patient:', error);
                newPatientStatusDiv.innerHTML =
                    '<div class="alert alert-danger">An error occurred while creating the patient.</div>';
            })
            .finally(() => {
                // Re-enable button
                saveNewPatientButton.disabled = false;
                saveNewPatientButton.innerHTML = originalText;
            });
    });
}

// Reset modal form when hidden
if (newPatientModal) {
    newPatientModal.addEventListener('hidden.bs.modal', function() {
        newPatientForm.reset();
        newPatientStatusDiv.innerHTML = '';
    });
}
</script>

<?php require_once 'includes/footer.php'; ?>